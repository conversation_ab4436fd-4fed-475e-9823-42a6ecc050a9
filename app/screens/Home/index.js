/* eslint-disable no-unused-expressions */
/* eslint-disable no-plusplus */
/* eslint-disable no-param-reassign */
/* eslint-disable global-require */
/* eslint-disable array-callback-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable no-undef */
/* eslint-disable max-len */
/* eslint-disable no-console */
/* eslint-disable quotes */
import React, { useCallback, useEffect, useState } from 'react';
import {
  AppState,
  BackHandler,
  Dimensions,
  FlatList,
  Image,
  Modal,
  // Share,
  Text,
  TouchableOpacity,
  View,
  Platform,
  ActivityIndicator,
  Linking,
  NativeModules,
  NativeEventEmitter,
  PermissionsAndroid,
  Alert,
} from 'react-native';
import FAIcon from 'react-native-vector-icons/FontAwesome';
// import RNFetchBlob from "rn-fetch-blob";
import Toast from 'react-native-simple-toast';
import {
  useFocusEffect,
  useTheme,
  useIsFocused,
} from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import {
  findIndex,
  flattenDeep,
  isArray,
  isEmpty,
  isObject,
  find,
} from 'lodash';
import { check, PERMISSIONS, request } from 'react-native-permissions';
import RNFetchBlob from 'rn-fetch-blob';
import Share from 'react-native-share';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
// import BackgroundFetch from "react-native-background-fetch";
import BleManager from 'react-native-ble-manager';
import { BleManager as BM } from 'react-native-ble-plx';
import _BackgroundTimer from 'react-native-background-timer';
import BluetoothStateManager from 'react-native-bluetooth-state-manager';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Icon from 'react-native-vector-icons/AntDesign';
import { AnimatedCircularProgress } from 'react-native-circular-progress';
import * as Progress from 'react-native-progress';
import CHeader from '../../components/CHeader';
import { CustomIcon } from '../../config/LoadIcons';
import { FontFamily } from '../../config/typography';
import styles from './styles';
import CVideoPlayer from '../../components/CVideoPlayer';
import CAlert from '../../components/CAlert';
import { translate } from '../../lang/Translate';
import CButton from '../../components/CButton';
import { getApiData } from '../../utils/apiHelper';
import BaseSetting from '../../config/setting';

import {
  addAction,
  openInAppBrowser,
  sendErrorReport,
} from '../../utils/commonFunction';
import InAppModal from '../../components/InAppModal';
import bluetoothActions from '../../redux/reducers/bluetooth/actions';
import AuthAction from '../../redux/reducers/auth/actions';
import BaseColors from '../../config/colors';
import StepPopup from '../../components/StepPopup';

let backPressed = 0;

/**
 *
 *@module Home
 *
 */

const BleManagerModule = NativeModules.BleManager;
const bleManagerEmitter = new NativeEventEmitter(BleManagerModule);

const manager = new BM({
  restoreStateIdentifier: 'BleInTheBackground',
  restoreStateFunction: restoredState => {
    if (restoredState == null) {
      console.log('BleManager was constructed for the first time');
    } else {
      console.log(
        'BleManager was restored. Check `restoredState.connectedPeripherals` property.',
      );
    }
  },
});

const Home = ({ navigation }) => {
  const dispatch = useDispatch();
  const {
    setDeviceID,
    setConnectedDeviceDetail,
    setLastDeviceId,
    setActiveDeviceId,
    setSwiperKey,
    setActiveChildDetail,
    setDeviceDetail,
    setConnectedDeviceDetails,
    setIsConnectLoad,
    setHomeDeviceClick,
    setLeftChildAlert,
    setClickAddQr,
    setSkipShow,
  } = bluetoothActions;
  const {
    setDeviceSettingOpen,
    setNotiCount,
    setCloseOnbording,
    setStep1Done,
    setStep2Done,
    setStep3Done,
    setStep4Done,
    setStep4bDone,
    setStep5Done,
    setStep5bDone,
    setStep5cDone,
    setStep5dDone,
    setStep5eDone,
    setStep5fDone,
    setStep6Done,
    setStep7,
    setStep8Done,
  } = AuthAction;
  const {
    connectedDeviceDetail,
    isBleConnected,
    lastDeviceId,
    emergencyAlert,
    connectedDeviceDetails,
    isLeftChildAlert,
    isConnecting,
    deviceDetail,
    deviceId,
  } = useSelector(state => state.bluetooth);
  const [videoPlayModal, setvideoPlayModal] = useState(false);
  const [alertModal, setAlertModal] = useState(false);
  const token = useSelector(state => state.auth.accessToken);
  const closeOnboarding = useSelector(state => state.auth.closeOnboarding);
  const step1Done = useSelector(state => state.auth.step1Done);
  const step2Done = useSelector(state => state.auth.step2Done);
  const step3Done = useSelector(state => state.auth.step3Done);
  const step8Done = useSelector(state => state.auth.step8Done);
  const onBordingDone = useSelector(state => state.auth.onBordingDone);
  const deviceSetting = useSelector(state => state.auth.deviceSetting);
  // const currentScreen = useSelector((state) => state.auth.currentScreen);
  const languageData = useSelector(state => state.language);
  const [postArr, setpostArr] = useState([]);
  const [inAppModal, setInAppModal] = useState(false);
  const [showStep1, setshowStep1] = useState(false);
  const [showStep2, setShowStep2] = useState(false);
  const [showStep3, setShowStep3] = useState(false);
  const [showStep8, setShowStep8] = useState(false);
  const [inAppMsgData, setinAppMsgData] = useState({});
  const [deviceList, setDeviceList] = useState([]);
  const [list, setList] = useState([]);
  const [postLoad, setPostLoad] = useState(true);
  const [isScanning, setIsScanning] = useState(false);
  const [devices, setDevices] = useState([
    {
      type: 'add',
    },
  ]);
  const colors = useTheme();
  const BaseColor = colors.colors;
  const [state, setSWState] = useState({
    key: '',
    value: null,
    isDisCon: true,
    isAutoConnect: '',
  });
  useEffect(() => {
    if (Platform.OS === 'android' && Platform.Version >= 31) {
      sendErrorReport(Platform.Version, 'platform version3');
      check(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
        .then(res => {
          console.log('BLUETOOTH_CONNECT--q-', res);
          if (res === 'granted') {
            // setBPermission(true);
          }
        })
        .catch(e => {
          console.log('bluetooth_', e);
        });
      request(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
        .then(result => {
          console.log('BLUETOOTH_CONNECT----1', result);
        })
        .then(statuses => {
          console.log(
            'BLUETOOTH_CONNECT--2',
            statuses[PERMISSIONS.ANDROID.BLUETOOTH_CONNECT],
          );
        });
    }
  }, []);
  // useEffect(() => {
  //   const handleAppStateChange = nextAppState =>
  //     appStateChangeHandler(nextAppState);
  //   const appStateChangeHandler = nextAppState =>
  //     handleAppStateChange(nextAppState);
  //   const appStateListener = AppState.addEventListener(
  //     'change',
  //     appStateChangeHandler,
  //   );
  //   return () => AppState.removeEventListener('change', appStateListener);
  // }, []);

  useEffect(() => {
    console.log('App ddddd');
    const handleAppStateChange = nextAppState => {
      // console.log(`App State: ${nextAppState}`);
      if (nextAppState === 'active') {
        // console.log('App State======', true);
        // sendErrorReport(true, "app_state_active");
      } else if (nextAppState === 'background') {
        // console.log('App State======', false);
        // sendErrorReport(false, "app_state_background");
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => subscription.remove();
  }, []);
  // open this check this
  useFocusEffect(
    useCallback(() => {
      if (
        !closeOnboarding &&
        !step1Done &&
        !deviceSetting &&
        deviceList?.length < 2
      ) {
        sendErrorReport(step1Done, 'step_1_show2_else');
        console.log('shw---step1', deviceList?.length);
        setshowStep1(true);
      }
      if (deviceList?.length >= 2) {
        dispatch(setStep8Done(true));
        setShowStep8(false);
      }
    }, [deviceList]),
  );

  useFocusEffect(
    useCallback(() => {
      if (!step8Done && onBordingDone && !closeOnboarding) {
        console.log('step8 done', step8Done);
        sendErrorReport(step8Done, 'step_8_show');
        setShowStep8(true);
      } else {
        // sendErrorReport(step8Done, "step_8_show_else");
        // if (
        //   !closeOnboarding &&
        //   !step1Done &&
        //   !deviceSetting &&
        //   deviceList?.length < 2
        // ) {
        //   sendErrorReport(step1Done, "step_1_show2_else");
        //   console.log("shw---step1", deviceList?.length);
        //   setshowStep1(true);
        // }
      }
      if (token !== '') {
        getPost('feed_post');
        getFeedPost();
        getDeviceList();
        getBadgeCount();
        getChildInfo();
        handleStopScan();
      }
    }, []),
  );
  useEffect(() => {
    getDeviceList();
  }, [isBleConnected, isConnecting, deviceDetail, deviceId]);

  useEffect(() => {
    if (Platform.OS === 'ios') {
      PushNotificationIOS.setApplicationIconBadgeNumber(0);
    }
  }, []);

  useEffect(() => {
    if (Platform.OS === 'android') {
      // if (!closeOnboarding && !step1Done && deviceList.length < 2) {
      //   console.log("shw---step1--1", deviceList.length);
      //   setshowStep1(true);
      // }
    } else {
      sendErrorReport(closeOnboarding, 'closeOnboarding');
      // sendErrorReport(step1Done, "step1Done");
      // sendErrorReport(deviceSetting, "deviceSetting");
      // if (
      //   !closeOnboarding &&
      //   !step1Done &&
      //   !deviceSetting &&
      //   deviceList.length < 2
      // ) {
      //   sendErrorReport(step1Done, "step_1_show2");
      //   console.log("shw---step1", deviceList.length);
      //   setshowStep1(true);
      // }
    }
  }, []);
  const isFocused = useIsFocused();
  useEffect(() => {
    if (Platform.OS === 'android') {
      if (!closeOnboarding && !step8Done && deviceList?.length < 2) {
        console.log('shw---step1--2', deviceList?.length);
        sendErrorReport(true, 'step1_show1');
        if (isFocused) {
          setshowStep1(true);
        }
      }
      console.log('shw---step1--222244', deviceList?.length);
      if (deviceList?.length >= 2) {
        setshowStep1(false);
      }
    }
  }, [deviceList]);
  // this function for get device list
  /** this function for get device list
   * @function getDeviceList
   * @param {object} data platform
   */
  async function getDeviceList() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    const newObj = {
      type: 'add',
    };

    const data = {
      platform: Platform.OS,
    };
    console.log('api called 2222');
    try {
      const response = await getApiData(
        BaseSetting.endpoints.connectedDevice,
        'POST',
        data,
        headers,
      );
      if (response.success && isArray(response.data)) {
        dispatch(setConnectedDeviceDetails(flattenDeep(response.data)));
        dispatch(setConnectedDeviceDetail(flattenDeep(response.data)));
        const arr = response.data;
        arr.unshift(newObj);
        setDeviceList(arr);
        console.log('cdevice list---step1', arr);
      } else {
        dispatch(setConnectedDeviceDetails([]));
        dispatch(setConnectedDeviceDetail([]));
        setDeviceList([newObj]);
        Toast.show(response.message);
      }
    } catch (error) {
      setDeviceList([newObj]);
      dispatch(setConnectedDeviceDetails([]));
      dispatch(setConnectedDeviceDetail([]));
      console.log('error for device list ===', error);
      sendErrorReport(error, 'get_device_list');
    }
  }

  // Test---------------------------------------------------->->

  let peripherals = new Map();
  const startScan = () => {
    if (!isScanning && !isBleConnected) {
      BleManager.scan([], 3, true)
        .then(() => {
          console.log('Scanning...');
          setIsScanning(true);
        })
        .catch(err => {
          console.error(err);
        });
    }
  };

  const onRefresh = () => {
    BleManager.stopScan().then(() => {
      // Success code
      console.log('Scan stopped');
      peripherals = new Map();
    });
    setTimeout(() => {
      startScan();
    }, 1000);
  };

  useEffect(() => {
    if (emergencyAlert || isLeftChildAlert) {
      // setTimeout(() => {
      getDeviceList();
      dispatch(setLeftChildAlert(false));
      onRefresh();
      // }, 100);
    }
  }, [emergencyAlert, isLeftChildAlert]);

  const handleStopScan = (item = {}) => {
    setIsScanning(false);
    if (list && !isEmpty(list) && item && !isEmpty(item)) {
      const ind = findIndex(list, i => i.id === item?.id);
      if (ind > -1) {
        list.splice(ind, 1);
        setList(list);
      }
    }
  };

  const handleDiscoverPeripheral = peripheral => {
    if (peripheral && !isBleConnected && !emergencyAlert) {
      if (!peripheral.name) {
        peripheral.name = 'NO NAME';
      }
      peripherals.set(peripheral.id, peripheral);
      setList(Array.from(peripherals.values()));
    }
  };

  useEffect(() => {
    if (!isBleConnected && !emergencyAlert) {
      BluetoothStateManager.getState().then(bluetoothState => {
        switch (bluetoothState) {
          case 'Unknown':
          case 'Resetting':
          case 'Unsupported':
          case 'Unauthorized':
          case 'PoweredOff':
            Toast.show(translate('turnOnBle'));
            if (Platform.OS === 'android') {
              sendErrorReport(true, 'requestToEnableHom');
              check(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT).then(res => {
                console.log('BLUETOOTH_CONNECT---', res);
                if (res === 'granted') {
                  BluetoothStateManager.requestToEnable().then(result => {
                    console.log(
                      'BluetoothStateManager.requestToEnable -> result',
                      result,
                    );
                  });
                }
              });
              request(PERMISSIONS.ANDROID.BLUETOOTH_CONNECT)
                .then(result => {
                  console.log('BLUETOOTH_CONNECT----1', result);
                })
                .then(statuses => {
                  console.log(
                    'BLUETOOTH_CONNECT--2',
                    statuses[PERMISSIONS.ANDROID.BLUETOOTH_CONNECT],
                  );
                });
            } else {
              console.log('open settings====');
              BluetoothStateManager.openSettings();
            }
            break;
          case 'PoweredOn':
            !isBleConnected && !emergencyAlert && token ? startScan() : null;
            break;
          default:
            break;
        }
      });
    }
  }, []);

  useEffect(() => {
    if (!isBleConnected && !emergencyAlert && !isLeftChildAlert) {
      bleManagerEmitter.addListener(
        'BleManagerCentralManagerWillRestoreState',
        data => {
          console.log(
            'BLE ==> BleManagerCentralManagerWillRestoreState ===> ',
            data,
          );
        },
      );

      bleManagerEmitter.addListener(
        'BleManagerDiscoverPeripheral',
        handleDiscoverPeripheral,
      );
      bleManagerEmitter.addListener('BleManagerStopScan', handleStopScan);
      // bleManagerEmitter.addListener(
      //   "BleManagerDisconnectPeripheral",
      //   handleDisconnectedPeripheral
      // );

      if (Platform.OS === 'android' && Platform.Version >= 23) {
        console.log('called---5');
        PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        ).then(result => {
          if (result) {
            console.log('Permission is OK');
          } else {
            PermissionsAndroid.request(
              PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
            ).then(res => {
              if (res) {
                console.log('User accept');
              } else {
                console.log('User refuse');
              }
            });
          }
        });
      }
    }

    return () => {
      console.log('unmount');
    };
  }, [isBleConnected, emergencyAlert, isLeftChildAlert]);

  React.useEffect(() => {
    const subscription = manager.onStateChange(s => {
      if (s === 'PoweredOn' && !isBleConnected && !emergencyAlert) {
        !isBleConnected ? startScan() : null;
        subscription.remove();
      }
    }, true);
    return () => subscription.remove();
  }, [manager]);

  //! This effect to autoconnect the device when back in range.
  useEffect(() => {
    // this will be executed once after 5 seconds
    console.log('toe');
    if (!isBleConnected && !emergencyAlert && !isLeftChildAlert) {
      _BackgroundTimer.runBackgroundTimer(async () => {
        const isConnect = await AsyncStorage.getItem('isConnect');
        // await getDeviceList();
        if (
          isConnect !== 'True' &&
          !isBleConnected &&
          !emergencyAlert &&
          !isLeftChildAlert &&
          lastDeviceId &&
          isEmpty(state.isAutoConnect) &&
          token &&
          deviceList?.length >= 2 // auto connect only if any device was available
        ) {
          // setTimeout(() => {
          onReadQR();
          // }, 1500);
          // getChildInfo();
        }
      }, 1500);
    }
  }, [isBleConnected, emergencyAlert, isLeftChildAlert, deviceList]);

  async function onReadQR() {
    const availableData = Array.from(peripherals.values());
    const arr = [...connectedDeviceDetails] || [];
    const result = arr.filter(obj2 =>
      availableData.some(obj1 => obj1?.name === obj2?.device_ssid),
    ); // assumes unique id

    let data = find(
      availableData,
      ar => ar?.name.toLowerCase() === result[0]?.device_ssid.toLowerCase(),
    );

    if (data && !isEmpty(data)) {
      dispatch(setIsConnectLoad(true));
      dispatch(setHomeDeviceClick(true));
      Toast.show(translate('connecting'));
    }

    handleStopScan(data);
    if (!data) {
      //! ----Check if Already Connected to same device in that case
      BleManager.getConnectedPeripherals([]).then(peripheralsArray => {
        if (!isEmpty(peripheralsArray)) {
          const isAlreadyConnected = find(
            peripheralsArray,
            lt =>
              lt?.name.toLowerCase() === result[0]?.device_ssid.toLowerCase(),
          );
          if (isAlreadyConnected) {
            data = isAlreadyConnected;
          }
        }
      });
    }
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };
    if (result.length > 0) {
      AsyncStorage.setItem('isConnect', 'True');
      try {
        const response = await getApiData(
          BaseSetting.endpoints.getDevice,
          'POST',
          {
            device_ssid: data?.name,
            product_id: data?.id,
            type: 'reconnect',
            lang_code: languageData?.languageData || 'es',
          },
          headers,
        );
        console.log(
          '🚀 ~ file: index.js ~ line 489 ~ onReadQR ~ response',
          response,
        );
        if (response.success && !isEmpty(response.data)) {
          sendErrorReport(response, 'response-----');
          // Torch.switchState(false);
          // dispatch(setEmergencyAlert(false));
          sendErrorReport(true, 'BleConnected_t');
          dispatch(bluetoothActions.setIsBleConnected(true));
          handleStopScan();
          onRefresh();
          sendErrorReport(true, 'deviceIdSet6');
          dispatch(bluetoothActions.setDeviceID(result[0]?.product_id));
          if (isArray(connectedDeviceDetail)) {
            const obj = { ...response?.data };
            console.log('🚀obj', obj);
            obj.product_id = result[0]?.product_id;

            const arry = [...connectedDeviceDetail] || [];
            const index = findIndex(
              arry,
              lt => lt?.product_id === obj?.product_id,
            );
            if (index > -1) {
              arry[index] = obj;
            }
            console.log(
              '🚀 ~ file: index.js ~ line 509 ~ onReadQR ~ arry',
              arry,
            );
            // dispatch(bluetoothActions.setConnectedDeviceDetail(arry));
            dispatch(bluetoothActions.setConnectedDeviceDetails(arry));
            dispatch(bluetoothActions.setSwiperKey(obj?.product_id));
            dispatch(bluetoothActions.setActiveDeviceId(obj));
            // dispatch(setChildProductId({ product_id: result[0]?.product_id, child_id: response.data.child_id }));
            const d = devices.findIndex(i => {
              const f = findIndex(
                i.deviceDetails,
                m => m.product_id === obj?.product_id,
              );
              return f > -1;
            });
            if (d > -1) {
              dispatch(setActiveChildDetail(devices[d]));
            }
            // getAutoConnect(
            //   response?.data?.product_id || "",
            //   response?.child_id || ""
            // );
            setSWState({ ...state, isDisCon: false, isAutoConnect: 'Yes' });
          }
          if (isObject(data) && !isEmpty(data)) {
            dispatch(bluetoothActions.setDeviceID(''));
            setTimeout(() => {
              sendErrorReport(true, 'deviceIdSet8');
              dispatch(bluetoothActions.setDeviceID(result[0]?.product_id));
              dispatch(bluetoothActions.setLastDeviceId(result[0]?.product_id));
            }, 2500);
          } else {
            setIsScanning(false);
            Toast.show("Can't find any device.");
            startScan();
          }
        } else {
          Toast.show(response.message);
        }
      } catch (error) {
        console.log('QR error From Get device API ===>>>>>>', error);
        sendErrorReport(error, 'on_read_qr');
      }
    }
  }

  // test<-<----------------------------------------------------

  /** this function for get Badge Count
   * @function getBadgeCount
   * @param {object} data {}
   */
  async function getBadgeCount() {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };

    try {
      const response = await getApiData(
        BaseSetting.endpoints.getUserAlertCount,
        'POST',
        {},
        headers,
      );

      if (response.success) {
        dispatch(setNotiCount(response));
      } else {
        console.log('error--->>');
      }
    } catch (error) {
      console.log('error for device list ===', error);
      sendErrorReport(error, 'get_device_list');
    }
  }

  /** this function for get Post's
   * @function getPost
   * @param {object} data platform
   */
  const getPost = () => {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };
    getApiData(
      BaseSetting.endpoints.getPost,
      'POST',
      {
        // campaign_type: type,
        platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
      },
      headers,
    )
      .then(response => {
        if (response.success) {
          console.log('response.data****', response?.data);
          setinAppMsgData(response?.data);
          setTimeout(() => {
            setInAppModal(true);
          }, 2000);
        } else {
          Toast.show('No Posts');
        }
      })
      .catch(err => {
        console.log('ERRR', err);
        Toast.show('Something went wrong while getting posts');
        sendErrorReport(err, 'get_post');
      });
  };
  const getChildInfo = () => {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };
    // console.log('getChildInfo -> headers', headers);

    getApiData(
      BaseSetting.endpoints.getUserChild,
      'POST',
      {
        platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
      },
      headers,
    )
      .then(response => {
        if (response.success) {
          const tempArr = [
            {
              type: 'add',
            },
          ];
          const childArr = response.data;
          const selectedChild = childArr.find(
            item => item.product_id === deviceId,
          );
          console.log('childdd aaaa home', childArr);
          //added this if auto connect after deleted device
          if (selectedChild?.device_connection === 'Active') {
            sendErrorReport(deviceId, 'read_data_device_home');
            if (deviceId) {
              sendErrorReport(deviceId, 'read_data_deviceID_Home');
              onReadQR();
            } else {
              if (isBleConnected) {
                dispatch(bluetoothActions.setDeviceID(''));
              }
            }
          }
          dispatch(setDeviceDetail(response.data));

          childArr.map(item => {
            tempArr.unshift(item);
          });

          setDevices(tempArr);
        } else {
          Toast.show(response.message);
        }
      })
      .catch(err => {
        // console.log("ERRR", err);
        Toast.show('Something went wrong while getting child detail');
        sendErrorReport(err, 'get_child_in_device4');
      });
  };

  // this function for get feed post data
  /** this function for get feed post data
   * @function getFeedPost
   * @param {object} data platform
   */
  async function getFeedPost() {
    setPostLoad(true);
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.getFeedPost,
        'POST',
        {
          platform: Platform.OS === 'ios' ? 'IOS' : 'ANDROID',
        },
        headers,
      );

      if (response.success) {
        console.log('feeed post data====', response.data);
        // const firstObj = {
        //   attachment_type: 'Video link',
        //   brand_id: 75,
        //   button_info: null,
        //   campaign_name: 'Hello World',
        //   campaign_tags: 'sdfsdf',
        //   campaign_type: 'feed_post',
        //   category_id: 0,
        //   createdAt: 1629435944757,
        //   createdBy: 75,
        //   description: 'Hello World',
        //   end_date: null,
        //   icon_url: null,
        //   id: 138,
        //   is_archived: 0,
        //   is_disable: 0,
        //   launch_url: 'https://www.youtube.com/watch?v=PeA0NrEGY8U',
        //   like: 0,
        //   message: 'sadas',
        //   message_position: null,
        //   post_file:
        //     'https://chillbaby-space.ams3.digitaloceanspaces.com/campaign_attachments/36591954-56de-40a4-9e46-2053fcef4070.png',
        //   post_subtitle: 'Hello World',
        //   post_title: 'BabyAuto car seat installation guide',
        //   segment_id: 0,
        //   start_date: null,
        //   status: 'Delivered',
        //   text_info: null,
        //   triggers: null,
        //   updatedAt: 1682941103637,
        // };
        setpostArr([...response.data]);
      } else {
        Toast.show(response.message);
      }
      setPostLoad(false);
    } catch (error) {
      setPostLoad(false);
      sendErrorReport(error, 'get_feed_post');
      console.log('feed post error ===', error);
    }
  }

  /** this function for get liked feed post data
   * @function getLikeFeedPost
   * @param {object} data campaign_id
   */
  async function getLikeFeedPost(id) {
    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.campaignlike,
        'POST',
        {
          campaign_id: id,
        },
        headers,
      );

      if (response.success) {
        getFeedPost();
        // setPostLike(response.data);
      } else {
        Toast.show(response.message);
      }
      // setPostLoad(false);
    } catch (error) {
      // setPostLoad(false);
      sendErrorReport(error, 'get_feed_post');
      console.log('feed post error ===', error);
    }
  }

  // this function for disconnect device if connected
  async function connectDevice(item) {
    console.log('🚀 ~ file: index.js ~ line 691 ~ connectDevice ~ item', item);
    // sendErrorReport(item, "Home_click_item");
    if (item?.connected === 1) {
      setTimeout(() => {
        dispatch(setActiveDeviceId(item));
        dispatch(setSwiperKey(item?.product_id));
        navigation.navigate(translate('dashboard'));
      }, 1000);
    } else {
      console.log('db connected else ----------');
      try {
        dispatch(setHomeDeviceClick(true));
        dispatch(setIsConnectLoad(true));
        // dispatch(setDeviceID(""));
        sendErrorReport(true, 'deviceIdSet10');
        dispatch(setDeviceID(item.product_id));
        dispatch(setLastDeviceId(item.product_id));
        // dispatch(setChildProductId({ product_id: item.product_id, child_id: item.child_id }));
        setTimeout(() => {
          const d = devices.findIndex(i => {
            const f = findIndex(
              i.deviceDetails,
              m => m.product_id === item.product_id,
            );
            return f > -1;
          });
          if (d > -1) {
            dispatch(setActiveChildDetail(devices[d]));
          }
          // sendErrorReport(devices[d], "devices[d]");
          // dispatch(setDeviceID(item.product_id));
          // dispatch(setLastDeviceId(item.product_id));

          const arr = [...connectedDeviceDetail] || [];
          const index = findIndex(
            arr,
            lt => lt?.product_id === item?.product_id,
          );
          if (index > -1) {
            arr[index] = item;
            sendErrorReport(item, 'item-----');
            // dispatch(setActiveDeviceId(item));
          } else {
            arr.push(item);
          }
          dispatch(setConnectedDeviceDetail(arr));
          dispatch(setConnectedDeviceDetails(arr));
          dispatch(setActiveDeviceId(item));
          dispatch(setSwiperKey(item?.product_id));
          // getAutoConnect(item?.product_id, item?.child_id);
          navigation.navigate(translate('dashboard'));
        }, 1000);
      } catch (error) {
        sendErrorReport(error, 'home_product_Error');
      }
    }
  }

  // async function getAutoConnect(id, childId) {
  //   const headers = {
  //     "Content-Type": "application/json",
  //     authorization: token ? `Bearer ${token}` : "",
  //   };
  //   try {
  //     const response = await getApiData(
  //       BaseSetting.endpoints.autoConn,
  //       "POST",
  //       {
  //         product_id: id,
  //         child_id: childId,
  //       },
  //       headers
  //     );

  //     if (response.success) {
  //       getChildInfo();
  //       getDeviceList();
  //     } else {
  //       // Toast.show("error===>>>", response.message);
  //     }
  //   } catch (error) {
  //     sendErrorReport(error, "autoConnect");
  //     console.log("feed post error ===", error);
  //   }
  // }

  // this function for handle media url
  function handleUrl(item) {
    if (
      item?.launch_url &&
      (item.launch_url.includes('http://') ||
        item.launch_url.includes('https://'))
    ) {
      openInAppBrowser(item?.launch_url);
      addAction(item, 'clicked', token);
    } else {
      Toast.show(translate('urlError'));
    }
  }

  const renderDevice = ({ item }) => (
    <View
      style={[styles.deviceCard, { backgroundColor: BaseColor.whiteColor }]}>
      {item.type === 'add' ? (
        <TouchableOpacity
          style={{ justifyContent: 'center', alignItems: 'center' }}
          activeOpacity={0.7}
          onPress={() => {
            dispatch(setClickAddQr(true));
            dispatch(setSkipShow(false));

            navigation.navigate('QRScanner');
          }}>
          <CustomIcon name="image-2" size={24} color={BaseColor.blueDark} />
          <View style={styles.row}>
            {/* <CustomIcon name="plus" color={BaseColor.blueDark} size={12} /> */}
            <Text
              style={{
                color: BaseColor.blueDark,
                fontFamily: FontFamily.default,
                // marginStart: 4,
                fontSize: 14,
                marginTop: 4,
                textAlign: 'center',
              }}>
              {translate('addNew')}
            </Text>
          </View>
          <Text
            style={{
              color: BaseColor.blackColor,
              fontFamily: FontFamily.default,
              // marginStart: 4,
              fontSize: 10,
              marginTop: 4,
              textAlign: 'center',
            }}>
            {translate('homeAddSmartDevice')}
          </Text>
        </TouchableOpacity>
      ) : (
        <TouchableOpacity
          style={{ ...styles.deviceCard, marginEnd: 0 }}
          activeOpacity={0.7}
          onPress={() => {
            connectDevice(item);
          }}>
          <Image
            source={{ uri: item.device_image }}
            style={{
              height: '100%',
              width: '100%',
            }}
            resizeMode="cover"
          />
          {item?.connected === 1 && isBleConnected ? null : (
            <View
              style={{
                position: 'absolute',
                top: 3,
                backgroundColor: 'white',

                paddingHorizontal: 3,
                borderTopStartRadius: 13,
                borderTopEndRadius: 13,
                paddingVertical: 4,
              }}>
              <Text
                style={{
                  color: 'black',
                  fontSize: 12,
                }}>
                {translate('press_to_connect')}
              </Text>
            </View>
          )}
          {item?.nick_name && (
            <View
              style={{
                position: 'absolute',
                flex: 1,
                flexDirection: 'row',
                alignItems: 'center',
                bottom: 7,
                left: 5,
                backgroundColor: '#fff',
                borderRadius: 5,
              }}>
              <Image
                source={
                  item?.child_profile
                    ? { uri: item?.child_profile }
                    : require('../../assets/images/logo.png')
                }
                style={[
                  styles.childImgStyle,
                  { borderColor: BaseColor.black60 },
                ]}
              />
              <Text
                style={[styles.childNameStyle, { color: BaseColor.blackColor }]}
                numberOfLines={1}>
                {item?.nick_name}
              </Text>
            </View>
          )}
          {emergencyAlert && item?.last_disconnected === 1 ? (
            <View
              style={[
                {
                  position: 'absolute',
                  height: 28,
                  width: 28,
                  borderRadius: 18,
                  // opacity: 1,
                  bottom: 3,
                  right: 3,
                  justifyContent: 'center',
                  alignItems: 'center',
                },
              ]}>
              <CustomIcon name="warning" color={BaseColor.alertRed} size={18} />
            </View>
          ) : isConnecting ? (
            <View
              style={[
                {
                  position: 'absolute',
                  height: 28,
                  width: 28,
                  borderRadius: 18,
                  bottom: 3,
                  right: 3,
                  justifyContent: 'center',
                  alignItems: 'center',
                },
              ]}>
              <ActivityIndicator color={BaseColor.org} />
            </View>
          ) : item?.connected === 1 && isBleConnected ? (
            <View
              style={[
                {
                  position: 'absolute',
                  height: 28,
                  width: 28,
                  borderRadius: 50,
                  bottom: 5,
                  right: 0,
                  backgroundColor:
                    item?.connected === 1 && isBleConnected
                      ? BaseColor.bDelight
                      : null,
                },
              ]}>
              <View
                style={[
                  {
                    position: 'absolute',
                    height: 18,
                    width: 18,
                    borderRadius: 50,
                    bottom: 5,
                    right: 5,
                    backgroundColor:
                      item?.connected === 1 && isBleConnected
                        ? BaseColor.bLight
                        : null,
                  },
                ]}>
                <View
                  style={[
                    {
                      position: 'absolute',
                      height: 12,
                      width: 12,
                      borderRadius: 18,
                      // opacity: 1,
                      bottom: 3,
                      right: 3,
                      justifyContent: 'center',
                      alignItems: 'center',
                      backgroundColor:
                        item?.connected === 1 && isBleConnected
                          ? BaseColor.blueDark
                          : BaseColor.alertRed,
                    },
                  ]}
                />
              </View>
            </View>
          ) : (
            <View
              style={[
                {
                  position: 'absolute',
                  height: 28,
                  width: 28,
                  borderRadius: 18,
                  bottom: 3,
                  right: 3,
                  justifyContent: 'center',
                  alignItems: 'center',
                },
              ]}>
              <CustomIcon
                name="link_off"
                color={BaseColor.alertRed}
                size={18}
              />
            </View>
          )}
        </TouchableOpacity>
      )}
    </View>
  );

  const shareImage = (url, item) => {
    const shareResponse = Share.open({
      url,
      message: `Shared Post ${item?.launch_url}`,
    })
      .then(res1 => {
        console.log(res1);
      })
      .catch(err => {
        console.log(err);
      });
    console.log(shareResponse);
  };

  const onShare = async (item, type) => {
    try {
      if (type === 'share') {
        const { fs } = RNFetchBlob;
        let imagePath = null;
        RNFetchBlob.config({
          fileCache: true,
        })
          .fetch('GET', item?.post_file)
          // the image is now dowloaded to device's storage
          .then(resp => {
            // the image path you can use it directly with Image component
            imagePath = resp.path();
            return resp.readFile('base64');
          })
          .then(base64Data => {
            // here's base64 encoded image
            const base64 = `data:image/png;base64,${base64Data}`;
            shareImage(base64, item);
            // remove the file from storage
            return fs.unlink(imagePath);
          });
      } else {
        const result = await Share.open({
          message: `Shared Post ${item?.launch_url}`,
          // url: shareText,
        });
        if (result.action === Share.sharedAction) {
          if (result.activityType) {
            // shared with activity type of result.activityType
          } else {
            // shared
          }
        } else if (result.action === Share.dismissedAction) {
          // dismissed
        }
      }
    } catch (error) {
      console.log('error: ==>', error);
    }
  };

  const renderGuide = ({ item }) => (
    <View
      style={[styles.guideRootView, { backgroundColor: BaseColor.whiteColor }]}>
      <TouchableOpacity
        activeOpacity={0.7}
        style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
        onPress={() => {
          // setvideoPlayModal(true);
          handleUrl(item);
        }}>
        {item?.post_file ? (
          <Image
            source={{ uri: item?.post_file }}
            style={{ height: 180, width: '100%' }}
          />
        ) : (
          <View
            style={{
              height: 180,
              width: '100%',
              backgroundColor: '#ccc',
            }}
          />
        )}
        {item?.attachment_type === 'Video link' ? (
          <View
            style={{
              position: 'absolute',
              // alignSelf: 'center',
              justifyContent: 'center',
              backgroundColor: BaseColor.whiteColor,
              borderRadius: 40,
              alignItems: 'center',
              height: 54,
              width: 54,
            }}>
            <FAIcon name="play" size={24} color={BaseColors.ramaGreen} />
          </View>
        ) : null}
      </TouchableOpacity>
      <View
        style={[
          styles.row,
          {
            marginTop: 0,
            paddingHorizontal: 12,
            padding: 6,
          },
        ]}>
        <Text style={{ flex: 1, color: BaseColor.blackColor }}>
          {item.post_title}
        </Text>
        <TouchableOpacity
          style={{ padding: 8 }}
          activeOpacity={0.7}
          onPress={() => {
            // Clipboard.setString(item.media_link);
            getLikeFeedPost(item.id);
          }}>
          <FAIcon
            name={item.like === 1 ? 'heart' : 'heart-o'}
            color={BaseColor.red}
            size={16}
          />
        </TouchableOpacity>
        {/* <TouchableOpacity
            style={{ padding: 8 }}
            activeOpacity={0.7}
            onPress={() => {
              handleLaunchUrl(item);
            }}
          >
            <MIcon name="launch" color={BaseColor.black70} size={16} />
          </TouchableOpacity> */}
        {item.post_file ? (
          <TouchableOpacity
            style={{ padding: 8 }}
            activeOpacity={0.7}
            onPress={() => {
              // Share.share({
              //   message: `Share Feed ${item.media_link} `,
              //   url: item.media_link,
              // });
              onShare(item, 'share');
              addAction(item, 'shared', token);
            }}>
            <CustomIcon name="send-2" color={BaseColor.black70} size={16} />
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );

  function handleBackButtonClick() {
    if (backPressed > 0) {
      BackHandler.exitApp();
      backPressed = 0;
    } else {
      backPressed++;
      Toast.show('Press Again To Exit');
      setTimeout(() => {
        backPressed = 0;
      }, 2000);
      return true;
    }
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        'hardwareBackPress',
        handleBackButtonClick,
      );
    };
  }, []);

  const { uuid } = useSelector(state => state.auth);
  const { fcmToken } = useSelector(state => state.notification);

  /**
   * Temporary debugging Push Notification function
   * @function tempHandleAlert
   */
  const tempHandleAlert = async (type = '') => {
    console.log(
      '??????????????_________________>>>>>>>>',
      'uuid',
      uuid,
      'fcm',
      fcmToken,
      'acessTok',
      token,
    );

    const headers = {
      'Content-Type': 'application/json',
      authorization: token ? `Bearer ${token}` : '',
    };
    const data = {
      title: 'Test push notification',
      body: 'Just Want to test the function is push notification working or not....',
      app_state: 'background',
      platform: Platform.OS,
      type: '',
      screen: 'home',
      objData: {
        campaign_type: 'push_message',
      },
    };
    try {
      await getApiData(BaseSetting.endpoints.addAlert, 'POST', data, headers);
    } catch (error) {
      console.log('add alert error ===', error);
    }
  };
  return (
    <>
      <View style={{ flex: 1, backgroundColor: BaseColor.whiteColor }}>
        <CHeader
          image={require('../../assets/images/logo.png')}
          leftIconName="settings-2"
          rightIconName="notifications-bell-button"
          onLeftPress={() => {
            navigation.openDrawer();
          }}
          onRightPress={() => {
            navigation.navigate('Alerts');
          }}
        />
        <View style={styles.smartDeviceView}>
          <FlatList
            data={deviceList}
            renderItem={renderDevice}
            horizontal
            contentContainerStyle={{ paddingStart: 16 }}
            showsHorizontalScrollIndicator={false}
          />
        </View>
        <View style={styles.videoView}>
          <View style={{ paddingHorizontal: 15, paddingVertical: 15 }}>
            <Text
              style={{
                color: BaseColor.blackColor,
                fontFamily: FontFamily.bold,
              }}>
              {postArr?.length > 0 && translate('whatsNew')}
            </Text>
          </View>
          <FlatList
            data={postArr}
            renderItem={renderGuide}
            contentContainerStyle={{
              paddingHorizontal: 16,
              paddingBottom: 100,
            }}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={() => (
              <View style={styles.emptyComponent}>
                {postLoad ? (
                  <ActivityIndicator color={BaseColor.whiteColor} />
                ) : (
                  <Text
                    style={{
                      padding: 8,
                      width: '100%',
                      fontSize: 16,
                      color: BaseColor.blackColor,
                      textAlign: 'center',
                    }}>
                    {/* {translate('noPosts')} */}
                  </Text>
                )}
              </View>
            )}
          />
        </View>
        <Modal
          visible={videoPlayModal}
          onRequestClose={() => setvideoPlayModal(false)}
          style={{ flex: 1 }}>
          <View style={{ flex: 1 }}>
            <CVideoPlayer
              source="http://techslides.com/demos/sample-videos/small.mp4"
              repeat
              onError={err => {
                console.log('err', err);
              }}
              MainViewStyle={{
                height: Dimensions.get('window').height,
                width: Dimensions.get('window').width,
              }}
            />
          </View>
          <CButton
            iconname="cancel"
            iconsize={10}
            iconColor={BaseColor.blackColor}
            style={styles.closeBtn}
            onPress={() => {
              setvideoPlayModal(false);
            }}
          />
        </Modal>
        <CAlert
          visible={alertModal}
          onRequestClose={() => setAlertModal(false)}
          alertMessage={translate('homeAlertMsg')}
          alertTitle={translate('homeAlertTitle')}
          onCancelPress={() => {
            setAlertModal(false);
          }}
        />
        {Platform.OS === 'ios' && deviceSetting && (
          <CAlert
            visible={deviceSetting}
            type="settingAlert"
            onRequestClose={() => dispatch(setDeviceSettingOpen(false))}
            agreeTxt={translate('locationSettingsAlert')}
            onOkPress={() => {
              Linking.openSettings();
              dispatch(setDeviceSettingOpen(false));
              setTimeout(() => {
                sendErrorReport(closeOnboarding, 'on_ok_press');
                if (!closeOnboarding && !step8Done && deviceList?.length < 2) {
                  sendErrorReport(closeOnboarding, 'step1_in');
                  setshowStep1(true); // m
                }
              }, 300);
            }}
            alertTitle={translate('locationSettingsTitle')}
            onCancelPress={() => {
              console.log('cancel pressed===', step1Done, closeOnboarding);
              dispatch(setDeviceSettingOpen(false));
              setTimeout(() => {
                console.log('jjjjjopen step1 if2', deviceList);
                sendErrorReport(closeOnboarding, 'on_cancel_press');
                if (!closeOnboarding && !step8Done && deviceList?.length < 2) {
                  sendErrorReport(closeOnboarding, 'step1_in_cancel');
                  setshowStep1(true); // m
                }
              }, 300);
            }}
          />
        )}
        {!isEmpty(inAppMsgData) ? (
          <InAppModal
            visible={inAppModal}
            button={inAppMsgData?.button_info || []}
            title={!isEmpty(inAppMsgData) ? inAppMsgData?.text_info : {}}
            image={!isEmpty(inAppMsgData) ? inAppMsgData?.post_file : ''}
            position={
              !isEmpty(inAppMsgData) ? inAppMsgData?.message_position : ''
            }
            onClose={() => {
              setinAppMsgData({});
              setInAppModal(false);
              addAction(inAppMsgData, 'clicked', token);
            }}
          />
        ) : null}
        <Modal
          visible={showStep1}
          style={{ flex: 1, padding: 16 }}
          transparent
          animationType="fade">
          <TouchableOpacity
            activeOpacity={1}
            style={{
              flex: 1,
              backgroundColor: '#00000050',
              justifyContent: 'center',
              alignItems: 'center',
              padding: 26,
              paddingTop: 44,
            }}>
            <View
              style={{
                backgroundColor: '#fafafa',
                borderRadius: 12,
                width: '90%',
              }}>
              <TouchableOpacity
                style={{
                  height: 50,
                  width: 50,
                  borderRadius: 25,
                  backgroundColor: BaseColor.blueDark,
                  position: 'absolute',
                  alignSelf: 'center',
                  top: -25,
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 100,
                }}>
                <Icon
                  name="setting"
                  style={{ color: BaseColor.whiteColor, fontSize: 30 }}
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={{
                  height: 35,
                  width: 35,
                  borderRadius: 18,
                  borderWidth: 2,
                  backgroundColor: BaseColor.whiteColor,
                  borderColor: BaseColor.blueDark,
                  position: 'absolute',
                  right: -10,
                  top: -15,
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 100,
                }}
                onPress={() => {
                  setshowStep1(false);
                  dispatch(setCloseOnbording(true));
                  dispatch(setStep8Done(true));
                }}>
                <FAIcon
                  name="close"
                  style={{ color: BaseColor.blueDark, fontSize: 20 }}
                />
              </TouchableOpacity>

              <Text
                style={{
                  marginHorizontal: 20,
                  color: BaseColor.textGrey,
                  marginTop: 30,
                  marginBottom: 10,
                  textAlign: 'center',
                }}>
                {translate('clickOn')}
                <Text style={{ fontWeight: 'bold', fontStyle: 'italic' }}>
                  {translate('addnew')}
                </Text>
                {translate('buttonConnectLinko')}
              </Text>

              <View
                style={{
                  paddingBottom: 12,
                  paddingHorizontal: 20,
                  // alignItems: "center",
                }}>
                <TouchableOpacity
                  style={{
                    backgroundColor: BaseColor.blueDark,
                    alignItems: 'center',
                    padding: 12,
                    borderRadius: 10,
                    marginVertical: 0,
                    paddingHorizontal: 25,
                  }}
                  onPress={() => {
                    setshowStep1(false);
                    dispatch(setStep1Done(true));
                    setShowStep2(true);
                  }}>
                  <Text
                    style={{
                      color: BaseColor.whiteColor,
                      fontSize: 16,
                    }}>
                    {translate('Start')}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </TouchableOpacity>
        </Modal>

        <StepPopup
          visible={showStep2}
          descriptionText={translate('step2')}
          image={require('../../assets/images/step2.png')}
          onNext={() => {
            dispatch(setStep2Done(true));
            setShowStep2(false);
            setShowStep3(true);
          }}
        />
        <StepPopup
          visible={showStep3}
          descriptionText={translate('step3a')}
          descriptionText2={translate('step3b')}
          image={require('../../assets/images/step3.jpg')}
          onNext={() => {
            dispatch(setStep3Done(true));
            setShowStep3(false);
            dispatch(setStep4Done(false));
            dispatch(setStep4bDone(false));
            dispatch(setStep5Done(false));
            dispatch(setStep5bDone(false));
            dispatch(setStep5cDone(false));
            dispatch(setStep5dDone(false));
            dispatch(setStep5eDone(false));
            dispatch(setStep5fDone(false));
            dispatch(setStep6Done(false));
            dispatch(setStep7(false));
            dispatch(setStep8Done(false));
          }}
        />
        <StepPopup
          visible={showStep8}
          title={translate('step8a')}
          descriptionText={translate('step8b')}
          descriptionText2={translate('step8c')}
          onNext={() => {
            dispatch(setStep8Done(true));
            setShowStep8(false);
          }}
          step8
        />
      </View>
    </>
  );
};

export default Home;
