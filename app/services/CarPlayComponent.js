/**
 * CarPlay Component for handling notifications and alerts
 * Integrates with react-native-carplay for CarPlay functionality
 */

import React, { useEffect, useState, useCallback, useRef } from 'react';
import { NativeEventEmitter, NativeModules, View } from 'react-native';
import {
  CarPlay,
  GridTemplate,
  AlertTemplate,
  ActionSheetTemplate,
  InformationTemplate,
  ListTemplate,
} from 'react-native-carplay';
import { useSelector } from 'react-redux';
import messaging from '@react-native-firebase/messaging';
import { sendErrorReport } from '../utils/commonFunction';

// Safe destructuring to avoid undefined errors
const MaxRCTCarPlayNotificationManager =
  NativeModules.MaxRCTCarPlayNotificationManager || null;

if (!MaxRCTCarPlayNotificationManager) {
  console.log(
    'Available modules:',
    Object.keys(NativeModules).filter(key => key.includes('CarPlay')),
  );
}

const CarPlayComponent = props => {
  const { navigation } = props;
  const user = useSelector(state => state.auth.userData);
  const { alertData, activeChildDetail } = useSelector(
    state => state.bluetooth,
  );
  const pendingNotification = useRef(null);
  const [carPlayConnected, setCarPlayConnected] = useState(CarPlay.connected);
  const [carPlayForeground, setCarPlayForeground] = useState(false);
  const carPlayForegroundRef = useRef(carPlayForeground);

  useEffect(() => {
    if (CarPlay.bridge && CarPlay.bridge.checkForConnection) {
      CarPlay.bridge.checkForConnection();
    }
  }, []);
  useEffect(() => {
    carPlayForegroundRef.current = carPlayForeground;
    console.log(
      '🚀 ~ CarPlayComponent ~ carPlayForegroundRef.current:',
      carPlayForegroundRef.current,
    );
    setCarPlayForeground(carPlayForegroundRef.current);
  }, [carPlayForeground]);

  // Add this useEffect right after the component initialization
  useEffect(() => {
    // Check and sync CarPlay connection status on component mount
    const checkCarPlayConnection = () => {
      if (CarPlay.connected !== carPlayConnected) {
        setCarPlayConnected(CarPlay.connected);
        sendErrorReport(CarPlay.connected, 'carplay_connected');

        // If CarPlay is connected and we have a pending notification, show it
        if (CarPlay.connected && pendingNotification.current) {
          handleNotification(pendingNotification.current);
          pendingNotification.current = null;
        }
      }
    };

    // Check immediately
    checkCarPlayConnection();

    // Also trigger the native check for connection
    if (CarPlay.bridge && CarPlay.bridge.checkForConnection) {
      CarPlay.bridge.checkForConnection();
    }
  }, []); // Empty dependency array - only run on mount

  // Add this useEffect to monitor state changes
  useEffect(() => {
    // If there's a mismatch, log it
    if (CarPlay.connected !== carPlayConnected) {
      console.warn('⚠️ State mismatch detected!');
      console.log('  - Native says:', CarPlay.connected);
      console.log('  - State says:', carPlayConnected);
    }
  }, [carPlayConnected]);

  // Connection handlers
  const onConnect = useCallback(() => {
    sendErrorReport('true', 'carplay_connected_onconnect');
    setCarPlayConnected(true);

    if (pendingNotification.current) {
      // Use setTimeout to ensure state has updated
      setTimeout(() => {
        handleNotification(pendingNotification.current);
        pendingNotification.current = null;
      }, 100);
    }
  }, []); // Remove handleNotification dependency to avoid stale closure

  const onDisconnect = useCallback(() => {
    sendErrorReport('false', 'carplay_connected_onDisconnect');
    setCarPlayConnected(false);
  }, []); // Remove handleNotification dependency to avoid stale closure

  // Convert push notification to CarPlay notification
  const processPushNotification = useCallback(notification => {
    try {
      console.log('Processing push notification for CarPlay:', notification);
      sendErrorReport(
        notification,
        'carplay_notification_processPushNotification',
      );

      // Extract notification data
      const title = notification.title || 'Notification';
      const message = notification.body || notification.message || '';

      // Determine notification type based on priority or content
      let type = 'alert'; // Default type

      // Check for action sheet notifications
      if (notification.actions && notification.actions.length > 1) {
        type = 'actionSheet';
      }

      // Prepare actions if available
      const actions = notification.actions || [];

      // Display notification in CarPlay
      handleNotification({
        title,
        message,
        type,
        actions,
        onActionPressed: id => {
          // Handle action callbacks if needed
          if (notification.onActionPressed) {
            notification.onActionPressed(id);
          }
        },
      });
    } catch (error) {
      console.error('Error processing push notification for CarPlay:', error);
    }
  }, []);

  useEffect(() => {
    if (alertData && alertData.message) {
      sendErrorReport(alertData, 'alertData_carplay');
      handleNotification({
        title: alertData?.title,
        message: alertData?.message,
        type: 'alert',
        actions: alertData?.actions || [],
      });
    }
  }, [alertData, handleNotification]);

  // Handle notifications
  const handleNotification = useCallback(
    notification => {
      sendErrorReport(notification, 'carplay_notification');
      console.log('🚀 ~ CarPlayComponent ~ notification:', notification);
      sendErrorReport(
        CarPlay.connected,
        'carplay_connected_handleNotification',
      );

      try {
        const { title, message, type = 'alert', actions = [] } = notification;
        // Use CarPlay.connected as the primary source of truth
        const isCarPlayReady = CarPlay.connected;
        console.log('🚀 ~ CarPlayComponent ~ isCarPlayReady:', isCarPlayReady);

        // if (!isCarPlayReady) {
        //   pendingNotification.current = notification;
        //   return;
        // }
        console.log('carPlayForegroundRef.current carPlayForegroundcarPlayForegroundcarPlayForeground:', carPlayForeground);
        sendErrorReport(carPlayForeground, 'carplay_foreground');
        if (!carPlayForeground) {
          if (MaxRCTCarPlayNotificationManager) {
            MaxRCTCarPlayNotificationManager.sendCarPlayNotification(
              title || 'Notification',
              message,
            )
              .then(res => {
                sendErrorReport(res, 'carplay_banner_send');
                console.log('✅ CarPlay notification result:', res);
              })
              .catch(err => {
                sendErrorReport(err, 'carplay_banner_send_error');
                console.error('❌ Error sending CarPlay notification:', err);
              });
          } else {
            console.warn('❌ CarPlay notification manager not available');
          }
          return;
        }

        if (type === 'alert') {
          const alertTemplate = new AlertTemplate({
            titleVariants: [message],
            actions:
              actions.length > 0
                ? actions
                : [
                    {
                      id: 'ok',
                      title: 'OK',
                    },
                  ],
            onActionButtonPressed: ({ id }) => {
              CarPlay.dismissTemplate();
              if (notification.onActionPressed) {
                notification.onActionPressed(id);
              }
            },
          });

          sendErrorReport(notification, 'carplay_notification_alert');

          // Try to present the template
          try {
            CarPlay.presentTemplate(alertTemplate, true);
            console.log('✅ AlertTemplate presented successfully');
          } catch (error) {
            console.error('❌ Failed to present AlertTemplate:', error);
          }
        } else if (type === 'actionSheet') {
          console.log('📋 Creating ActionSheetTemplate');

          const actionSheetTemplate = new ActionSheetTemplate({
            title: title,
            message: message,
            actions:
              actions.length > 0
                ? actions
                : [
                    {
                      id: 'ok',
                      title: 'OK',
                    },
                    {
                      id: 'cancel',
                      title: 'Cancel',
                      style: 'cancel',
                    },
                  ],
            onActionButtonPressed: ({ id }) => {
              CarPlay.dismissTemplate();
              if (notification.onActionPressed) {
                notification.onActionPressed(id);
              }
            },
          });

          try {
            CarPlay.presentTemplate(actionSheetTemplate, true);
            console.log('✅ ActionSheetTemplate presented successfully');
          } catch (error) {
            console.error('❌ Failed to present ActionSheetTemplate:', error);
          }
        }
      } catch (error) {
        console.error('❌ Error handling CarPlay notification:', error);
        console.error('Error stack:', error.stack);
      }
    },
    [carPlayConnected, carPlayForeground],
  );

  // Set up Firebase messaging listeners
  useEffect(() => {
    // Listen for foreground messages
    const unsubscribeForeground = messaging().onMessage(async remoteMessage => {
      sendErrorReport(remoteMessage, 'foreground_notification_carplay');
      sendErrorReport(carPlayConnected, 'foreground_carPlayConnected_carplay');
      if (carPlayConnected) {
        processPushNotification({
          title: remoteMessage.notification?.title,
          body: remoteMessage.notification?.body,
          ...remoteMessage.data,
        });
      }
    });
    // Listen for background/quit state messages when app is opened
    messaging().onNotificationOpenedApp(remoteMessage => {
      sendErrorReport(remoteMessage, 'background_notification_carplay');
      sendErrorReport(carPlayConnected, 'background_carPlayConnected_carplay');
      if (carPlayConnected) {
        processPushNotification({
          title: remoteMessage.notification?.title,
          body: remoteMessage.notification?.body,
          ...remoteMessage.data,
        });
      }
    });

    // Check for initial notification (app opened from quit state)
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          sendErrorReport(remoteMessage, 'initial notification_carplay');
          sendErrorReport(carPlayConnected, 'initial_carPlayConnected_carplay');
          if (carPlayConnected) {
            processPushNotification({
              title: remoteMessage.notification?.title,
              body: remoteMessage.notification?.body,
              ...remoteMessage.data,
            });
          }
        }
      });

    return () => {
      unsubscribeForeground();
    };
  }, [carPlayConnected, processPushNotification]);

  // This is for Know State of CarPlay
  useEffect(() => {
    const carPlayEmitter = new NativeEventEmitter(
      NativeModules.CarPlayEventEmitter,
    );

    const fg = carPlayEmitter.addListener('carPlayForeground', () => {
      onConnect();
      console.log('✅ CarPlay foreground event received');
      sendErrorReport('true', 'CarPlay_foreground_detected');
      setCarPlayForeground(true);
    });

    const bg = carPlayEmitter.addListener('carPlayBackground', () => {
      console.log('✅ CarPlay background event received');
      sendErrorReport('true', 'CarPlay_backgrounnd_detected');
      setCarPlayForeground(false);
    });

    return () => {
      fg.remove();
      bg.remove();
    };
  }, []);

  // Set up CarPlay event listeners
  useEffect(() => {
    const emt = new NativeEventEmitter(NativeModules.RNCarPlay);
    const connectListener = emt.addListener('didConnect', onConnect);

    // Set up notification listener
    const notificationEmitter = new NativeEventEmitter(
      NativeModules.MaxRCTCarPlayNotificationManager,
    );

    const notificationListener = notificationEmitter.addListener(
      'carPlayNotification',
      notification => {
        handleNotification(notification);
      },
    );

    CarPlay.registerOnConnect(onConnect);
    CarPlay.registerOnDisconnect(onDisconnect);

    return () => {
      connectListener.remove();
      notificationListener?.remove();
      CarPlay.unregisterOnConnect(onConnect);
      CarPlay.unregisterOnDisconnect(onDisconnect);
    };
  }, [onConnect, onDisconnect, CarPlay]);

  // Set up main grid template
  useEffect(() => {
    console.log('CarPlay.connectedCarPlay.connected', CarPlay.connected);
    const infoTemplate = new InformationTemplate({
      title: `ChillBaby${user?.full_name ? ` - ${user.full_name}` : ''}`,
      subtitle: 'Your personalized dashboard in CarPlay',
      actions: [], // you can add actions if needed
      items: [
        {
          title: 'Welcome',
          detail: user?.full_name || 'Guest User',
        },
        {
          title: 'Status',
          detail: carPlayConnected
            ? '✅ Connected to CarPlay'
            : '❌ Not Connected',
        },
      ],
    });

    CarPlay.setRootTemplate(infoTemplate);
  }, [carPlayConnected, CarPlay.connected, user, handleNotification]);

  return <View></View>;
};

export default CarPlayComponent;
